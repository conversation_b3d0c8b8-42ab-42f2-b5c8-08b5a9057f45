import { useState } from 'react';
import BeginMatchingStep from './matchingStep/beginMatchingStep';
import CandidateReviewStep from './matchingStep/candidateReviewStep';

interface MatchingStepProps {
  currentStep?: number;
}

export default function MatchingStep({ currentStep = 0 }: MatchingStepProps) {
  const [beginMatchingCompleted, setBeginMatchingCompleted] = useState(false);
  const [candidateReviewCompleted, setCandidateReviewCompleted] = useState(false);

  const handleBeginMatchingSubmit = () => {
    setBeginMatchingCompleted(true);
    console.log('Begin matching completed');
  };

  const handleCandidateReviewSubmit = () => {
    setCandidateReviewCompleted(true);
    console.log('Candidate review completed');
  };

  return (
    <div className="space-y-8">
      <BeginMatchingStep
        onSubmit={handleBeginMatchingSubmit}
        isCompleted={beginMatchingCompleted}
      />

      <CandidateReviewStep
        onSubmit={handleCandidateReviewSubmit}
        isCompleted={candidateReviewCompleted}
      />
    </div>
  );
}
