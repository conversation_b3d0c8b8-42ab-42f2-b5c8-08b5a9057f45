import { useState } from 'react';
import VisaApplicationStep from './travelStep/visaApplicationStep';
import TravelArrangementsStep from './travelStep/travelArrangementsStep';

interface TravelStepProps {
  currentStep?: number;
}

export default function TravelStep({ currentStep = 0 }: TravelStepProps) {
  const [visaApplicationCompleted, setVisaApplicationCompleted] = useState(false);
  const [travelArrangementsCompleted, setTravelArrangementsCompleted] = useState(false);

  const handleVisaApplicationSubmit = () => {
    setVisaApplicationCompleted(true);
    console.log('Visa application completed');
  };

  const handleTravelArrangementsSubmit = () => {
    setTravelArrangementsCompleted(true);
    console.log('Travel arrangements completed');
  };

  return (
    <div className="space-y-8">
      <VisaApplicationStep
        onSubmit={handleVisaApplicationSubmit}
        isCompleted={visaApplicationCompleted}
      />

      <TravelArrangementsStep
        onSubmit={handleTravelArrangementsSubmit}
        isCompleted={travelArrangementsCompleted}
      />
    </div>
  );
}
