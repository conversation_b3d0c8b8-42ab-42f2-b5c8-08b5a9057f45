import { useState } from 'react';
import ProofOfAddressStep from './complianceStep/proofOfAddressStep';
import LegalResidenceStep from './complianceStep/legalResidenceStep';
import IncomeReviewStep from './complianceStep/incomeReviewStep';

export default function ComplianceStep() {
  const [proofOfAddressCompleted, setProofOfAddressCompleted] = useState(false);
  const [legalResidenceCompleted, setLegalResidenceCompleted] = useState(false);
  const [incomeReviewCompleted, setIncomeReviewCompleted] = useState(false);

  const handleProofOfAddressSubmit = () => {
    setProofOfAddressCompleted(true);
    console.log('Proof of address completed');
  };

  const handleLegalResidenceSubmit = () => {
    setLegalResidenceCompleted(true);
    console.log('Legal residence completed');
  };

  const handleIncomeReviewSubmit = () => {
    setIncomeReviewCompleted(true);
    console.log('Income review completed');
  };

  return (
    <div className="space-y-8">
      <ProofOfAddressStep
        onSubmit={handleProofOfAddressSubmit}
        isCompleted={proofOfAddressCompleted}
      />

      <LegalResidenceStep
        onSubmit={handleLegalResidenceSubmit}
        isCompleted={legalResidenceCompleted}
      />

      <IncomeReviewStep
        onSubmit={handleIncomeReviewSubmit}
        isCompleted={incomeReviewCompleted}
      />
    </div>
  );
}
