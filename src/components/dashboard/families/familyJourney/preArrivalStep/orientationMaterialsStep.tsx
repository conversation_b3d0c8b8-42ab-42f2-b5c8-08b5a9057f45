import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { FileText } from "lucide-react"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

interface OrientationMaterialsStepProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
}

export default function OrientationMaterialsStep({
  onSubmit,
  isCompleted = false
}: OrientationMaterialsStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Orientation materials step submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-blue-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <FileText className={`h-6 w-6 ${isCompleted ? 'text-blue-500' : 'text-gray-600'}`} />
              <span className="text-lg font-semibold">6.2 Orientation Materials</span>
              <Badge className={isCompleted ? 'bg-blue-500 text-white' : 'bg-gray-600 text-white'}>
                {isCompleted ? 'Completed' : 'Pending'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="text-center py-12">
                  <FileText className="h-20 w-20 text-blue-400 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">Download Preparation Guides</h3>
                  <p className="text-gray-600 mb-6 text-lg max-w-md mx-auto">
                    Access orientation materials and preparation guides for your au pair's arrival.
                  </p>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-700 px-4 py-2 text-lg">
                    Available After Room Preparation
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  )
}
