import { useState } from 'react';
import AirportPickupStep from './arrivalStep/airportPickupStep';
import FirstWeekSetupStep from './arrivalStep/firstWeekSetupStep';

interface ArrivalStepProps {
  currentStep?: number;
}

export default function ArrivalStep({ currentStep = 0 }: ArrivalStepProps) {
  const [airportPickupCompleted, setAirportPickupCompleted] = useState(false);
  const [firstWeekSetupCompleted, setFirstWeekSetupCompleted] = useState(false);

  const handleAirportPickupSubmit = () => {
    setAirportPickupCompleted(true);
    console.log('Airport pickup completed');
  };

  const handleFirstWeekSetupSubmit = () => {
    setFirstWeekSetupCompleted(true);
    console.log('First week setup completed');
  };

  return (
    <div className="space-y-8">
      <AirportPickupStep
        onSubmit={handleAirportPickupSubmit}
        isCompleted={airportPickupCompleted}
      />

      <FirstWeekSetupStep
        onSubmit={handleFirstWeekSetupSubmit}
        isCompleted={firstWeekSetupCompleted}
      />
    </div>
  );
}
