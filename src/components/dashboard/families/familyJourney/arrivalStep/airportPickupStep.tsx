import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface AirportPickupStepProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
}

export default function AirportPickupStep({
  onSubmit,
  isCompleted = false
}: AirportPickupStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Airport pickup step submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-green-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <MapPin className={`h-6 w-6 ${isCompleted ? 'text-green-500' : 'text-gray-600'}`} />
              <span className="text-lg font-semibold">7.1 Airport Pickup</span>
              <Badge className={isCompleted ? 'bg-green-500 text-white' : 'bg-gray-600 text-white'}>
                {isCompleted ? 'Completed' : 'Pending Previous Steps'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="text-center py-12">
                  <MapPin className="h-20 w-20 text-green-400 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">Confirm Arrival Pickup</h3>
                  <p className="text-gray-600 mb-6 text-lg max-w-md mx-auto">
                    This step will become available once your au pair is ready to arrive.
                  </p>
                  <Button
                    className="bg-green-500 text-white hover:bg-green-600"
                    onClick={handleSubmit}
                    disabled={!isCompleted}
                  >
                    CONFIRM PICKUP ARRANGEMENTS
                  </Button>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
