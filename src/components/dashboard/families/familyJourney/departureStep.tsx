import { useState } from 'react';
import FinalEvaluationStep from './departureStep/finalEvaluationStep';
import DepartureChecklistStep from './departureStep/departureChecklistStep';

export default function DepartureStep() {
  const [finalEvaluationCompleted, setFinalEvaluationCompleted] = useState(false);
  const [departureChecklistCompleted, setDepartureChecklistCompleted] = useState(false);

  const handleFinalEvaluationSubmit = () => {
    setFinalEvaluationCompleted(true);
    console.log('Final evaluation completed');
  };

  const handleDepartureChecklistSubmit = () => {
    setDepartureChecklistCompleted(true);
    console.log('Departure checklist completed');
  };

  return (
    <div className="space-y-8">
      <FinalEvaluationStep
        onSubmit={handleFinalEvaluationSubmit}
        isCompleted={finalEvaluationCompleted}
      />

      <DepartureChecklistStep
        onSubmit={handleDepartureChecklistSubmit}
        isCompleted={departureChecklistCompleted}
      />
    </div>
  );
}
