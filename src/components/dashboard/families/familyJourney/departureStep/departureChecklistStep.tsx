import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface DepartureChecklistStepProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
}

export default function DepartureChecklistStep({
  onSubmit,
  isCompleted = false
}: DepartureChecklistStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Departure checklist step submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-red-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <CheckCircle className={`h-6 w-6 ${isCompleted ? 'text-red-500' : 'text-gray-600'}`} />
              <span className="text-lg font-semibold">9.2 Departure Checklist</span>
              <Badge className={isCompleted ? 'bg-red-500 text-white' : 'bg-gray-600 text-white'}>
                {isCompleted ? 'Completed' : 'Pending'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="text-center py-12">
                  <CheckCircle className="h-20 w-20 text-red-400 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">Final Departure Tasks</h3>
                  <p className="text-gray-600 mb-6 text-lg max-w-md mx-auto">
                    Complete the final departure checklist and program closure tasks.
                  </p>
                  <Badge variant="secondary" className="bg-red-100 text-red-700 px-4 py-2 text-lg">
                    Available After Final Evaluation
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
