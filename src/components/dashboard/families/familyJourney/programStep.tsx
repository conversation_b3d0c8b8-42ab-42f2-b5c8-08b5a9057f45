import { useState } from 'react';
import MonthlyCheckInsStep from './programStep/monthlyCheckInsStep';
import SupportResourcesStep from './programStep/supportResourcesStep';

interface ProgramStepProps {
  currentStep?: number;
}

export default function ProgramStep({ currentStep = 0 }: ProgramStepProps) {
  const [monthlyCheckInsCompleted, setMonthlyCheckInsCompleted] = useState(false);
  const [supportResourcesCompleted, setSupportResourcesCompleted] = useState(false);

  const handleMonthlyCheckInsSubmit = () => {
    setMonthlyCheckInsCompleted(true);
    console.log('Monthly check-ins completed');
  };

  const handleSupportResourcesSubmit = () => {
    setSupportResourcesCompleted(true);
    console.log('Support resources completed');
  };

  return (
    <div className="space-y-8">
      <MonthlyCheckInsStep
        onSubmit={handleMonthlyCheckInsSubmit}
        isCompleted={monthlyCheckInsCompleted}
      />

      <SupportResourcesStep
        onSubmit={handleSupportResourcesSubmit}
        isCompleted={supportResourcesCompleted}
      />
    </div>
  );
}
