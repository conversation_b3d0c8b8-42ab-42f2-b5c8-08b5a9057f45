import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Download } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface IntakeMeetingStepProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
  isExpanded?: boolean;
}

export default function IntakeMeetingStep({
  onSubmit,
  isCompleted = false,
  isExpanded = false
}: IntakeMeetingStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Intake meeting confirmation submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible value={isExpanded ? "item-1" : ""} className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <CheckCircle className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-gray-600'}`} />
              <span className="text-lg font-semibold">1.3 Intake Meeting & Guidelines</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-gray-600 text-white'}>
                {isCompleted ? 'Completed' : 'In Progress'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="md:col-span-2 space-y-6">
                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">VIDEO INTAKE MEETING</h4>
                      <p className="text-gray-700 text-lg leading-relaxed">
                        To understand your family situation and provide detailed information about the process and legal
                        requirements, we will schedule a video call during office hours.
                      </p>
                    </div>

                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">HOST-FAMILY GUIDELINES</h4>
                      <p className="text-gray-700 text-lg leading-relaxed">
                        The Host Family Guidelines include legal and practical information, as well as tips on
                        communication, personal interaction, and handling cultural differences.
                      </p>
                    </div>

                    <div className="flex items-center space-x-3 p-6 bg-gray-50 rounded-lg border-2 border-gray-200">
                      <Checkbox id="confirm-guidelines" checked={isCompleted} disabled={isCompleted} />
                      <label htmlFor="confirm-guidelines" className="text-lg font-semibold text-black">
                        Confirm Guidelines
                      </label>
                    </div>
                  </div>

                  <div className="bg-stone-900 text-white p-8 rounded-lg">
                    <h3 className="text-xl font-bold mb-6">Host Family Guidelines</h3>
                    <p className="text-sm mb-6 text-gray-300">Download and read this document</p>
                    <Button 
                      className="w-full bg-white text-black hover:bg-stone-600"
                      onClick={handleSubmit}
                      disabled={isCompleted}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      DOWNLOAD
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
